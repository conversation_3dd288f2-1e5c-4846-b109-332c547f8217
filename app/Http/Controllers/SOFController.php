<?php

namespace App\Http\Controllers;

use App\Models\SOF;
use App\Models\Attachment;
use App\Models\User;
use App\Services\AuditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Mail\UploadSofEmail;
use App\Models\Activity;
use App\Models\ActivityType;
use App\Models\Order;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;


class SOFController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('ott-portal.sof.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('ott-portal.sof.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'remark' => 'nullable|string',
            'quote_id' => 'nullable|exists:quotes,id',
        ]);

        // Create a new SOF
        $sof = SOF::create([
            'sof_id' => SOF::generateUniqueSOFId(),
            'remark' => $request->remark,
            'quote_id' => $request->quote_id,
            'created_by' => Auth::id(),
            'updated_by' => Auth::id(),
        ]);

        return redirect()->route('sof.index')->with('success', 'SOF created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, $id)
    {
        if ($id === 'all') {
            $sofs = SOF::with(['creator', 'updater', 'quote'])->get();

            $data = $sofs->map(function ($sof) {
                return [
                    'sof_id' => $sof->sof_id,
                    'remark' => $sof->remark,
                    'quote_id' => $sof->quote ? "<a href='" . route('quote.view', ['quote' => $sof->quote->id]) . "' class='px-1 text-primary' data-bs-toggle='tooltip' data-bs-placement='bottom' data-bs-title='View Quote'>" . $sof->quote->quote_id . "</a>" : null,
                    'created_by' => $sof->creator ? $sof->creator->name : 'N/A',
                    'updated_by' => $sof->updater ? $sof->updater->name : 'N/A',
                    'created_at' => $sof->created_at->format('d-m-Y H:i'),
                    'updated_at' => $sof->updated_at->format('d-m-Y H:i'),
                    'action' => "
                        <a href='" . route('sof.show', ['sof' => $sof->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='View'><i class='bx bx-file-find font-size-20'></i></a>
                        <a href='" . route('sof.edit', ['sof' => $sof->id]) . "' class='px-1 text-secondary' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Edit'><i class='bx bx-edit-alt font-size-20'></i></a>
                    ",
                ];
            });

            return response()->json(['data' => $data]);
        }

        // For individual SOF views, use route model binding with id
        $sof = SOF::with(['quote', 'attachments'])->findOrFail($id);
        return view('ott-portal.sof.view', compact('sof'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SOF $sof)
    {
        return view('ott-portal.sof.edit', compact('sof'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SOF $sof)
    {

        $rules = [
            'remark' => 'required|nullable|string',
            'sof_id' => 'required|nullable|exists:sofs,id',
            'sof_form' => 'required|nullable|file|mimes:pdf|max:2048',
            'isom' => 'required|array|min:1',
        ];

        try {
            DB::beginTransaction(); // Start transaction

            // Validate the request
            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation errors',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Modify isom array
            $isom = [];
            foreach ($request->isom as $staff) {
                // Extract name and email using regex or string splitting
                if (preg_match('/^(.*) \((.*)\)$/', $staff, $matches)) {
                    $user = User::whereRaw('LOWER(email) = ?', [trim(strtolower($matches[2]))])->first();
                    $isom[] = [
                        'user_id' => $user ? $user->id : null,
                        'name' => trim($matches[1]),
                        'email' => trim($matches[2]),
                        'role' => 'ISOM',
                    ];
                }
            }

            // Update the SOF
            $sof->remark = $request->remark;
            $sof->isom = json_encode($isom);
            $sof->updated_by = Auth::id();
            $sof->save();

            // Handle file upload
            if ($request->hasFile('sof_form')) {

                // Delete existing attachments
                $attachments = Attachment::where('attachable_id', $sof->id)
                    ->where('attachable_type', 'App\Models\SOF')
                    ->get();

                foreach ($attachments as $attachment) {
                    Storage::disk('public')->delete($attachment->path);
                    $attachment->delete();
                }

                // Store new attachment
                $file = $request->file('sof_form');

                $filename = $file->getClientOriginalName();
                // Store file in storage/app/public/attachments
                $path = $file->storeAs('attachments', $filename, 'public');

                // Create attachment record
                Attachment::create([
                    'attachable_id' => $sof->id,
                    'attachable_type' => 'App\Models\SOF',
                    'filename' => $filename,
                    'description' => "SOF Document",
                    'mime_type' => $file->getClientMimeType(),
                    'path' => $path,
                    'size' => $file->getSize()
                ]);
            }

            // Get the activity 'Upload SOF' status
            $activity = Activity::where('trackable_id', $sof->id)
                        ->where('trackable_type', 'App\Models\SOF')
                        ->whereHas('activityType', function ($query) {
                            $query->where('name', 'Upload SOF');
                        })
                        ->first();

            // Check if the activity is not done
            if ($activity && $activity->status !== 'Done') { $process = 'upload'; }
            else { $process = 're-upload'; } 

            // Prepare email data
            $emailData = [
                'process' => $process,
                'quote_id' => $sof->quote->quote_id,
                'customer_name' => $sof->quote->customer_name,
                'address' => $sof->quote->address->unit_street . ', ' . $sof->quote->address->housing_area . ', ' . $sof->quote->address->postcode . ' ' . $sof->quote->address->city . ', ' . $sof->quote->address->state,
                'sfdc_id' => $sof->quote->sfdc_id,
                'view_url' => route('sof.show', ['sof' => $sof->id], true),
                'quotePath' => storage_path('app/public/quote') . '/' . 'Quote_' . $sof->quote->quote_id . '.pdf',
            ];

            // Send email to recipient
            try {
                Mail::to(collect($isom)->pluck('email')->toArray())
                    ->cc(array_merge(
                        collect(json_decode($sof->quote->project_team))->pluck('email')->toArray(),
                        collect(json_decode($sof->quote->recipient_emails))->pluck('email')->toArray()
                    ))
                    ->send(new UploadSofEmail($sof, storage_path('app/public') . '/' . $path, $emailData));

                // Log the email sent
                Log::info('Email sent to ' . implode(', ', collect($isom)->pluck('email')->toArray()));
            } catch (Exception $e) {
                // Log the error message
                Log::error('Failed to send email: ' . $e->getMessage());
            }

            // Check process if upload
            if ($process == 'upload') {

                // Update activity 'Upload SOF' status to done
                $activity = $sof->activities()->whereHas('activityType', function ($query) {
                    $query->where('name', 'Upload SOF');
                })->first();

                if ($activity) {
                    $activity->status = 'Done';
                    $activity->actual_end_date = now();
                    $activity->actual_duration = $activity->actual_end_date->diffInDaysFiltered(function (Carbon $date) {
                        return !$date->isWeekend();
                    }, $activity->actual_start_date);
                    $activity->is_overdue = ($activity->actual_duration - $activity->activityType->duration > 0) ? true : false;
                    $activity->user_id = auth()->user()->id;
                    $activity->save();
                }

                // Generate Order
                $order = Order::create([
                    'order_id' => Order::generateUniqueOrderId(),
                    'sof_id' => $sof->id,
                    'quote_id' => $sof->quote_id,
                    'status' => 'pending',
                    'project_team' => json_encode(array_merge(
                        json_decode($sof->quote->project_team, true),
                        json_decode($sof->quote->recipient_email, true)
                    )),
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                ]);

                // Create a copy of address from quote to order
                $address = $sof->quote->address->replicate();
                $address->addressable_id = $order->id;
                $address->addressable_type = 'App\Models\Order';
                $address->save();

                // Create activity
                // Get activity type
                $activityType = ActivityType::where('name', 'Create Order')->first();

                // Calculate planned_end_date considering only working days
                $plannedEndDate = Carbon::now();
                $daysToAdd = $activityType->duration;
                while ($daysToAdd > 0) {
                    $plannedEndDate->addDay();
                    if (!$plannedEndDate->isWeekend()) {
                        $daysToAdd--;
                    }
                }

                // Create a new activity 'Create Order'
                $activity = Activity::create([
                    'activity_id' =>  (string) Activity::generateUniqueActivityId(),
                    'activity_type_id' => $activityType->id,
                    'trackable_id' => $order->id,
                    'trackable_type' => get_class($order),
                    'status' => 'in progress',
                    'planned_start_date' => now(),
                    'planned_end_date' => $plannedEndDate,
                    'actual_start_date' => now(),
                    'actual_end_date' => null,
                    'actual_duration' => null,
                    'aging' => null,
                    'is_overdue' => false,
                    'user_id' => null,
                    'updated_at' => now()->addMinutes(1)
                ]);

                // Create activity logs
                AuditService::logActivity(
                    'generated',
                    get_class($activity),
                    $activity->id,
                    'Activity ' . $activity->activityType->name . ' is created'
                );

                // Create activity logs
                AuditService::logActivity(
                    'generated',
                    get_class($activity),
                    $activity->id,
                    'Activity ' . $activity->activityType->name . ' is created'
                );
            }

            // Log activity
            AuditService::logActivity(
                'updated',
                get_class($sof),
                $sof->id,
                'Updated SOF ' . $sof->sof_id,
            );

            DB::commit(); // Commit transaction

            return response()->json([
                'success' => true,
                'message' => 'SOF (' . $sof->sof_id . ') updated successfully.',
            ]);
        }
        catch (Exception $e) {
            DB::rollBack(); // Rollback transaction

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating SOF.',
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SOF $sof)
    {
        $sof->delete();

        return redirect()->route('sof.index')->with('success', 'SOF deleted successfully.');
    }
}
