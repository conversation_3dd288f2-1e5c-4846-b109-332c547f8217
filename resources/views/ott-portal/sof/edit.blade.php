@extends('layouts.master')
@section('title')
    Edit SOF
@endsection
@section('css')
    <!-- Add any additional CSS here -->
    <link href="{{ URL::asset('/assets/libs/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />
@endsection

@section('content')
    @component('common-components.breadcrumb')
        @slot('pagetitle') Order Management @endslot
        @slot('title') Edit SOF @endslot
    @endcomponent

    <div class="row">
        <div class="col-12">
            <div class="card px-md-5 px-4">
                <div class="card-body">
                    <h5 class="card-title mb-1">Edit SOF</h5>
                    <small class="text-muted text-truncate mb-0"><i>Fill in all required (*) fields and click 'Update' button</i></small>
                    {{-- display message --}}
                    @if (session('success'))                        
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">
                            </button>
                        </div>
                    @endif

                    @if(session('errors'))
                        <div class="alert alert-danger">
                            <ul>
                                @foreach(session('errors')->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form id="sofForm" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="mb-3 mt-4 row">
                            <label for="sof_form" class="col-md-2 col-form-label">*SOF Form (*.pdf)</label>
                            <div class="col-md-10">
                                <input type="file" class="form-control" id="sof_form" name="sof_form">
                                @if(!$sof->attachments->isEmpty())
                                    @foreach($sof->attachments as $attachment)
                                        @if($attachment->description == 'SOF Document')
                                            <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" 
                                                class="btn btn-outline-secondary mt-2" target="_blank">
                                                Download SOF Form <i class="bx bx-download ms-1"></i>
                                            </a>
                                        @endif
                                    @endforeach
                                @endif
                                <small id="sof_form_error" class="text-danger error-message" style="display: none"></small>
                            </div>
                        </div>
                        <div class="mb-3 mt-4 row">
                            <label for="remark" class="col-md-2 col-form-label">Remark</label>
                            <div class="col-md-10">
                            <textarea class="form-control" id="remark" name="remark" rows="3">{{ old('remark', $sof->remark) }}</textarea>
                            <small id="remark_error" class="text-danger error-message" style="display: none"></small>
                            </div>
                        </div>

                        <div class="mb-3 mt-4 row">
                            <label for="isom" class="col-md-2 col-form-label">*Staff (ISOM)</label>
                            <div class="col-md-10">
                            <select class="form-control select2" id="isom" name="isom[]" multiple="multiple">
                            @if($sof->isom)
                                @foreach (json_decode($sof->isom) as $staff)
                                    <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                @endforeach
                            @endif
                            </select>
                            <small id="isom_error" class="text-danger error-message" style="display: none"></small>
                            </div>
                        </div>

                        <input type="hidden" id="sof_id" name="sof_id" value="{{ $sof->id }}">
                    </form>
                </div>
            </div>
            <div class="d-flex justify-content-end mt-3">
                <button type="button" class="btn btn-primary me-2" id="btn-update-sof">Update</button>
                <button type="button" class="btn btn-light" onclick="window.history.back();">Back</button>
            </div>
        </div>
    </div>

    {{-- Toast --}}
    <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
        <div id="success-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-success border-0">
                <div class="d-flex">
                    <div class="toast-body" id="success-toast-message">
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>
    <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
        <div id="failed-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-danger border-0">
                <div class="d-flex">
                    <div class="toast-body" id="failed-toast-message">
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script src="{{ URL::asset('/assets/libs/select2/select2.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for Search
            $('#isom').select2({
                placeholder: "Search for a staff...",
                ajax: {
                    url: "{{ route('staff.search') }}",  // Your API route
                    dataType: 'json',
                    delay: 250,  // Delay for API calls to prevent too many requests
                    data: function(params) {
                        return {
                            info: params.term,  // The search term entered by the user
                        };
                    },
                    processResults: function(data) {
                        // Map the API response into the format that Select2 expects
                        return {
                            results: data.results.map(function(item) {
                                return {
                                    id: item.Name + ' (' + item.email + ')',  // Use Staff_No as the ID
                                    text: item.Name + ' (' + item.email + ')'    // Display "Staff_No - Name" as the text
                                };
                            })
                        };
                    },
                    cache: true
                },
                multiple: true,  // Allow multiple selections
                minimumInputLength: 2  // Start showing results after 2 characters
            });

            // Enable CSRF Token for all ajax requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Auto-populate quote id
            var sof = @json($sof);
            $('.page-title').append(' [' + sof.sof_id + ']');

            // Click event to update SOF
            $('#btn-update-sof').click(function() {
                // Disable button to prevent double click and change text to updating....
                $(this).addClass('disabled').text('Updating....');

                // Reset error message
                $('.error-message').hide();

                var formData = new FormData();
                // formData.append('sof_form', $('#sof_form')[0].files[0]);
                formData.append('remark', $('#remark').val());  
                formData.append('sof_id', $('#sof_id').val());

                // For attachment
                const fileInput = $('#sof_form')[0];
                
                if (fileInput.files.length > 0) {
                    const file = fileInput.files[0];
                    formData.append('sof_form', file);
                }
                else {
                    formData.append('sof_form', '');
                }

                // Add isom staff to formData
                var isoms = $('#isom').val();
                if (Array.isArray(isoms)) {
                    isoms.forEach(function(isom, index) {
                        formData.append('isom[' + index + ']', isom);
                    });
                }

                // use patch method
                formData.append('_method', 'PATCH');

                $.ajax({
                    url: "{{ route('sof.update', $sof->id) }}",
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        console.log(response);
                        if (response.success) {
                            // Show the toast
                            $('#success-toast-message').text(response.message);
                            var toastElement = $('#success-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show();  // This show the toast
                            // Redirect to the quotation list page
                            setTimeout(function() {
                                window.location.href = "{{ route('sof.index') }}";
                            }, 1500);
                        }
                        else {
                            if (response.includes('Max size is 25MB')) {
                                // Show the toast
                                $('#failed-toast-message').html("Total attached file size cannot exceed 25MB.");
                                var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                                var toast = new bootstrap.Toast(toastElement, {
                                    autohide: true,     // Automatically hide after the delay
                                    delay: 3000         // Time in milliseconds before the toast hides
                                });
                                toast.show(); 
                            }
                        }
                        $('#btn-update-sof').text('Update').removeClass('disabled');
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            console.log(xhr.responseJSON.errors);
                            var errors = xhr.responseJSON.errors;
                            var errorHtml = '<ul>';
                            $.each(errors, function(key, value) {
                                errorHtml += '<li>' + value[0] + '</li>';
                            });
                            errorHtml += '</ul>';
                            // create error message
                            $.each(errors, function(key, value) {
                                var errorElement = $('#' + key + '_error');
                                if (errorElement.length) {
                                    errorElement.text(value[0]).show();
                                    // Focus on the field with the error
                                    $('#' + key).focus();
                                } else {
                                    errorHtml += '<li>' + value[0] + '</li>';
                                }
                            });

                            // Show the toast
                            $('#failed-toast-message').html("Please fill in all required fields.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        } else {
                            console.log(xhr.responseJSON);
                            // Show the toast
                            $('#failed-toast-message').html("Failed to update. Please check and retry.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        }

                        // Enable the button and change the text back
                        $('#btn-update-sof').text('Update').removeClass('disabled');
                    }
                });
            });
        });
    </script>
@endsection
