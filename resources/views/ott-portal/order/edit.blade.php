@extends('layouts.master')
@section('title')
    Edit Order
@endsection
@section('css')
    <!-- DataTables -->
    <link href="{{ URL::asset('/assets/libs/datatables/datatables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/dropzone/dropzone.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('/assets/libs/bootstrap-datepicker/bootstrap-datepicker.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ URL::asset('/assets/libs/datepicker/datepicker.min.css') }}">
@endsection

@section('content')
    <style>
        input[type="number"].no-spinner::-webkit-inner-spin-button,
        input[type="number"].no-spinner::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        input[type="number"].no-spinner {
            -moz-appearance: textfield;
        }
        #phone-number-summary td {
            border-top: 0.1rem #eee solid !important;
        }
        /* Add styles for duplicate phone number validation */
        .duplicate-phone-number {
            background-color: #ffebee !important;
            border: 2px solid #f44336 !important;
        }
        .duplicate-error-message {
            color: #f44336;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            font-weight: 500;
        }
        .submit-disabled {
            opacity: 0.6;
            pointer-events: none;
        }
        /* Remove border, background, and outline for the input */
        .plain-input {
            border: none;
            background: transparent;
            box-shadow: none;
        }

        /* Remove the outline on focus */
        .plain-input:focus {
            outline: none;
            box-shadow: none;
        }

        /* Remove the hover effect */
        .plain-input:hover {
            border: none;
        }
    </style>
    @component('common-components.breadcrumb')
        @slot('pagetitle') Order Management @endslot
        @slot('title') Edit Order @endslot
    @endcomponent
    <div class="row">
    <div class="col-xl-12">
        <div class="custom-accordion">
            <div class="card mb-2">
                <a href="#order-customerinfo-collapse" class="text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                {{-- <i class="uil uil-receipt text-primary h2"></i> --}}
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        01
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-1">Customer Profile</h5>
                                <small class="text-muted text-truncate mb-0"><i>Search Customer info from NOVA/ICP database (optional) and fill in all the required (*) fields</i></small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>
                    </div>
                </a>

                <div id="order-customerinfo-collapse" class="collapse show">
                    <div class="px-4 pb-3 pt-2">
                        <div class="row">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row d-none">
                                    <label for="order_id" class="col-md-4 col-form-label">Order Id.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Order Id." id="order_id" name="order_id" value="{{ $order->id ?? '' }}">
                                        <small id="order_id_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row" @if($order->customer->customer_type == 'existing') style="display: none;" @endif>
                                    <label for="search_customer" class="col-md-4 col-form-label">Search Customer</label>
                                    <div class="col-md-8 d-flex">
                                        <input class="form-control" type="text" id="search_customer" name="search_customer" value="" placeholder="Search Customer Info using BRN" autocomplete="off">
                                        <a href="#" class="btn btn-secondary ms-2" id="btn_search_customer">Search</a>
                                    </div>
                                    <div class="col-md-8 offset-md-4">
                                        <small id="search_customer_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="customer_name" class="col-md-4 col-form-label">*Customer Name</label>
                                    <div class="col-md-8">
                                        <input type="hidden" id="customer_id" name="customer_id" value="{{ $order->customer->id ?? '' }}">  
                                        <input class="form-control" type="text" placeholder="Customer Name" id="customer_name" name="customer_name" value="{{ $order->customer->name ?? '' }}"
                                         @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="customer_name_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="account_number" class="col-md-4 col-form-label">*Account Number</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Account Number" id="account_number" name="account_number" value="{{ $order->customer->account_no ?? '' }}"
                                         @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="account_number_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="sfdc_id" class="col-md-4 col-form-label">*SFDC Id.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="SFDC Id." id="sfdc_id" name="sfdc_id" value="{{ $order->customer->sfdc_id ?? '' }}" @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="sfdc_id_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="brn" class="col-md-4 col-form-label">*BRN</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="BRN" id="brn" name="brn" value="{{ $order->customer->brn ?? '' }}" @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="brn_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="segment_group" class="col-md-4 col-form-label">*Segment Group</label>
                                    <div class="col-md-8">
                                        <select class="form-select" id="segment_group" name="segment_group" @if($order->customer->customer_type == 'existing') disabled @endif>
                                            <option value="">Select Segment Group...</option>
                                            @foreach($lov['SEGMENTS'] as $segment)
                                                <option value="{{ $segment['SEGMENT_GROUP'] }}" {{ (isset($order->customer->segment_group) && $order->customer->segment_group == $segment['SEGMENT_GROUP']) ? 'selected' : '' }}>{{ $segment['SEGMENT_GROUP'] }}</option>
                                            @endforeach
                                        </select>
                                        <small id="segment_group_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                {{-- <div class="mb-2 mb-md-3 row">
                                    <label for="vertical" class="col-md-4 col-form-label">Vertical</label>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" id="vertical" name="vertical">
                                                    <option value="">Select Vertical...</option>
                                                    @foreach(\App\Models\Vertical::all() as $vertical)
                                                        <option value="{{ $vertical->name }}" {{ (isset($order->vertical->name) && $order->vertical->name == $vertical->name) ? 'selected' : '' }}>
                                                            {{ $vertical->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <small id="vertical_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                            <div class="col-md-6">
                                                <input class="form-control" type="text" placeholder="OTHERS (please specify)" id="vertical_others" name="vertical_others" value="{{ $order->vertical_others ?? '' }}">
                                                <small id="vertical_others_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="sale_segment" class="col-md-4 col-form-label">Sale Segment</label>
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" id="sale_segment" name="sale_segment">
                                                    <option value="">Select Sale Segment...</option>
                                                    @foreach(\App\Models\SaleSegment::all() as $saleSegment)
                                                        <option value="{{ $saleSegment->name }}" {{ (isset($order->sale_segment) && $order->sale_segment->name == $saleSegment->name) ? 'selected' : '' }}>
                                                            {{ $saleSegment->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <small id="sale_segment_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                            <div class="col-md-6">
                                                <input class="form-control" type="text" placeholder="OTHERS (please specify)" id="sale_segment_others" name="sale_segment_others" value="{{ $order->sale_segment_others ?? '' }}">
                                                <small id="sale_segment_others_error" class="text-danger error-message" style="display: none"></small>
                                            </div>
                                        </div>
                                    </div>
                                </div> --}}
                            </div>
                            <div class="col-12 col-md-6 px-md-4">
                                {{-- <div class="mb-2 mb-md-3 row">
                                    <label for="industry_code" class="col-md-4 col-form-label">Industry Code</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Industry Code" id="industry_code" name="industry_code" value="{{ $order->customer->industry_code ?? '' }}" @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="industry_code_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div> --}}
                                {{-- <div class="mb-2 mb-md-3 row">
                                    <label for="value_segment" class="col-md-4 col-form-label">Value Segment</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Value Segment" id="value_segment" name="value_segment" value="{{ $order->customer->value_segment ?? '' }}" @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="value_segment_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div> --}}
                                <div class="mb-2 mb-md-3 row">
                                    <label for="segment_code" class="col-md-4 col-form-label">*Segment Code</label>
                                    <div class="col-md-8">
                                        <select class="form-select" id="segment_code" name="segment_code" @if($order->customer->customer_type == 'existing') disabled @endif>
                                            <option value="">Select Segment Code...</option>
                                        </select>
                                        <small id="segment_code_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="segment_sub_group" class="col-md-4 col-form-label">*Segment Sub Group</label>
                                    <div class="col-md-8">
                                        <select class="form-select" id="segment_sub_group" name="segment_sub_group" @if($order->customer->customer_type == 'existing') disabled @endif>
                                            <option value="">Select Segment Sub Group...</option>
                                            @foreach($lov['SEGMENT_SUB_GROUP'] as $segment_sub_group)
                                                <option value="{{ $segment_sub_group }}" {{ (isset($order->customer->segment_sub_group) && $order->customer->segment_sub_group == $segment_sub_group) ? 'selected' : '' }}>{{ $segment_sub_group }}</option>
                                            @endforeach
                                        </select>
                                        <small id="segment_sub_group_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="person_in_charge" class="col-md-4 col-form-label">*Person In Charge</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Person In Charge" id="person_in_charge" name="person_in_charge" value="{{ $order->customer->person_in_charge ?? '' }}" @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="person_in_charge_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="position_title" class="col-md-4 col-form-label">*Position Title</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Position Title" id="position_title" name="position_title" value="{{ $order->customer->position_title ?? '' }}" @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="position_title_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="department" class="col-md-4 col-form-label">*Department</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Department" id="department" name="department" value="{{ $order->customer->department ?? '' }}" @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="department_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="contact_no" class="col-md-4 col-form-label">*Telephone No.</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Telephone No." id="contact_no" name="contact_no" value="{{ $order->customer->contact_no ?? '' }}" @if($order->customer->customer_type == 'existing') disabled @endif>
                                        <small id="contact_no_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                {{-- <div class="mb-2 mb-md-3 row justify-content-md-center d-none">
                                    <label for="prepared_by" class="col-md-4 col-form-label">Prepared By</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Prepared By" id="prepared_by" name="prepared_by" value="{{ Str::ucfirst(optional(Auth::user())->name ?? '') }}" readonly>
                                    </div>
                                </div> --}}
                                {{-- <div class="mb-2 mb-md-3 row">
                                    <label for="date" class="col-md-4 col-form-label">Date</label>
                                    <div class="col-md-8">
                                        <div class="input-group" id="datepicker">
                                            <input type="text" class="form-control" placeholder="Date" data-date-format="dd-mm-yyyy" data-date-container='#datepicker' data-provide="datepicker" name="date" id="date" value="{{ Carbon\Carbon::parse($order->created_at)->format('d-m-Y') ?? '' }}">
                                            <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                        </div><!-- input-group -->
                                    </div>
                                </div> --}}
                            </div>
                            <div class="col-12 px-md-4">
                                <div class="mb-0 mb-md-1 row">
                                    <label for="unit_street" class="col-md-2 col-form-label">*Address</label>
                                    <div class="col-md-5 px-md-1">
                                        <input type="hidden" id="address_id" name="address_id" value="">
                                        <input class="form-control" type="text" placeholder="Unit No, Street Name" id="unit_street" name="unit_street" value="">
                                        <small id="unit_street_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-5 ps-md-0">
                                        <input class="form-control" type="text" placeholder="Housing Area / Development Name" id="housing_area" name="housing_area" value="">
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="postcode" class="col-md-2 col-form-label d-none d-md-block"></label>
                                    <div class="col-md-3 px-md-1">
                                        <input class="form-control" type="text" placeholder="Postcode" id="postcode" name="postcode" value="">
                                        <small id="postcode_error" class="text-danger error-message" style="display: none"></small>
                                        <div id="postcode-loading" class="position-absolute top-50 end-0 translate-middle-y me-3" style="display: none;">
                                            <div class="spinner-border spinner-border-sm text-secondary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 ps-md-0 pe-md-1">
                                        <input class="form-control" type="text" placeholder="City" id="city" name="city" value="">
                                        <small id="city_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-4 ps-md-0">
                                        <input class="form-control" type="text" placeholder="State" id="state" name="state" value="">
                                        <small id="state_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                            </div>                            
                            <div class="col-12 px-md-4">
                                <div class="mb-3 row">
                                    <label for="checkBillingAddress" class="col-md-2 col-form-label"></label>
                                    <div class="col-md-5 px-md-1">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="checkBillingAddress">
                                            <label class="form-check-label" for="checkBillingAddress">
                                                Tick if Billing Address is the same as Main Address
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 px-md-4">
                                <div class="mb-0 mb-md-1 row">
                                    <label for="billing_unit_street" class="col-md-2 col-form-label">*Billing Address</label>
                                    <div class="col-md-5 px-md-1">
                                        <input type="hidden" id="billing_address_id" name="billing_address_id" value="">
                                        <input class="form-control" type="text" placeholder="Unit No, Street Name" id="billing_unit_street" name="billing_unit_street" value="">
                                        <small id="billing_unit_street_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-5 ps-md-0">
                                        <input class="form-control" type="text" placeholder="Housing Area / Development Name" id="billing_housing_area" name="billing_housing_area" value="">
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="billing_postcode" class="col-md-2 col-form-label d-none d-md-block"></label>
                                    <div class="col-md-3 px-md-1">
                                        <input class="form-control" type="text" placeholder="Postcode" id="billing_postcode" name="billing_postcode" value="">
                                        <small id="billing_postcode_error" class="text-danger error-message" style="display: none"></small>
                                        <div id="billing-postcode-loading" class="position-absolute top-50 end-0 translate-middle-y me-3" style="display: none;">
                                            <div class="spinner-border spinner-border-sm text-secondary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 ps-md-0 pe-md-1">
                                        <input class="form-control" type="text" placeholder="City" id="billing_city" name="billing_city" value="">
                                        <small id="billing_city_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-4 ps-md-0">
                                        <input class="form-control" type="text" placeholder="State" id="billing_state" name="billing_state" value="">
                                        <small id="billing_state_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#order-orderinfo-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                {{-- <i class="uil uil-truck text-primary h2"></i> --}}
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        02
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-1">Order Details</h5>
                                <small class="text-muted text-truncate mb-0"><i>Fill in all the required (*) fields</i></small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="order-customerinfo-collapse" class="collapse show">
                    <div class="px-4 pb-3 pt-2">
                        <div class="row">
                            <div class="col-12 col-md-6 px-md-4">
                                <div class="mb-2 mb-md-3 row">
                                    <label for="order_type" class="col-md-4 col-form-label">*Order Type</label>
                                    <div class="col-md-8">
                                        <select class="form-select" id="order_type" name="order_type">
                                            <option value="">Select Order Type...</option>
                                            {{-- FOR TEMPORARY WHILE ONLY SUPPORTING NEW INSTALL --}}
                                            <option value="New Install" selected>New Install</option>
                                            <option value="Modify" disabled>Modify</option>
                                            <option value="Terminate" disabled>Terminate</option>
                                        </select>
                                        <small id="order_type_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="contract_length" class="col-md-4 col-form-label">Contact (Months)</label>
                                    <div class="col-md-8">
                                        <input class="form-control" type="text" placeholder="Contract" id="contract_length" name="contract_length" value="{{ $order->quote->contract_length ?? '' }}" disabled>
                                        <small id="contract_length_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="quote_id" class="col-md-4 col-form-label">Quote Id.</label>
                                    {{-- <div class="col-md-8"> --}}
                                    <div class="col-md-8 d-flex">
                                            <input class="form-control" type="text" id="quote_id" name="quote_id" value="{{ $order->quote->quote_id ?? '' }}" disabled>
                                            @if($order->quote)
                                                <a href="{{ route('quote.view', ['quote' => $order->quote->id]) }}" class="btn btn-secondary ms-2">View</a>
                                            @endif
                                        </div>
                                    {{-- </div> --}}
                                </div>
                            </div>
                            <div class="col-12 col-md-6 px-md-4">
                            {{-- <div id="packages_users"></div> --}}
                                <div class="mb-2 row">    
                                    <label for="packages_users" class="col-md-3 col-form-label">Package & Users</label>
                                    <div class="col-md-8">
                                        <textarea name="packages_users" id="packages_users"  class="form-control" cols="30" rows="4" disabled></textarea>
                                        <small id="packages_users_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="attachments" class="col-md-3 col-form-label">SOF Form</label>
                                    {{-- <div class="col-md-8"> --}}
                                    <div class="col-md-8">
                                    @if($order->sof->attachments->isEmpty())
                                        <p>No attachments found.</p>
                                    @else
                                    @foreach($order->sof->attachments as $attachment)
                                        @if($attachment->description == 'SOF Document')
                                            <a href="{{ route('file.download', ['filename' => $attachment->filename, 'folder' => 'attachments']) }}" 
                                                class="btn btn-outline-secondary" target="_blank">
                                                Download <i class="bx bx-download ms-1"></i>
                                            </a>
                                        @endif
                                    @endforeach
                                    @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-2">
                <a href="#order-phonenumber-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                {{-- <i class="uil uil-truck text-primary h2"></i> --}}
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        03
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-1">Phone Numbers and Parameters Assignment</h5>
                                <small class="text-muted text-truncate mb-0"><i>Add new address or copy the existing address (optional), fill in number of users and select packages</i></small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="order-customerinfo-collapse" class="collapse show">
                    <div class="px-4 pb-3 pt-2">
                        <div class="row">
                            <div class="col-12 px-md-4">
                                <div class="mb-0 mb-md-1 row">
                                    <label for="new_unit_street" class="col-md-2 col-form-label">New Address</label>
                                    <div class="col-md-5 px-md-1">
                                        <input type="hidden" id="new_address_id" name="new_address_id" value="">
                                        <input class="form-control" type="text" placeholder="Unit No, Street Name" id="new_unit_street" name="new_unit_street" value="">
                                        <small id="new_unit_street_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-5 ps-md-0">
                                        <input class="form-control" type="text" placeholder="Housing Area / Development Name" id="new_housing_area" name="new_housing_area" value="">
                                    </div>
                                </div>
                                <div class="mb-2 mb-md-3 row">
                                    <label for="new_postcode" class="col-md-2 col-form-label d-none d-md-block"></label>
                                    <div class="col-md-3 px-md-1">
                                        <input class="form-control" type="text" placeholder="Postcode" id="new_postcode" name="new_postcode" value="">
                                        <small id="new_postcode_error" class="text-danger error-message" style="display: none"></small>
                                        <div id="new-postcode-loading" class="position-absolute top-50 end-0 translate-middle-y me-3" style="display: none;">
                                            <div class="spinner-border spinner-border-sm text-secondary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 ps-md-0 pe-md-1">
                                        <input class="form-control" type="text" placeholder="City" id="new_city" name="new_city" value="">
                                        <small id="new_city_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                    <div class="col-md-4 ps-md-0">
                                        <input type="hidden" id="new_state_id" name="new_state_id" value="">
                                        <input class="form-control" type="text" placeholder="State" id="new_state" name="new_state" value="">
                                        <small id="new_state_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 px-md-4">
                                <div class="row justify-content-md-end">
                                    <div class="col-12 col-md-6">
                                        <div class="mt-0 row">
                                            {{-- <label class="col-md-4 col-form-label"></label> --}}
                                            <div class="col-md-12 text-end d-flex justify-content-end align-items-center">
                                                <button type="button" class="btn btn-secondary" id="btn-add-address" name="btn-add-address">
                                                    <i class="uil uil-plus me-1"></i> Add Address 
                                                </button> 
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 px-md-4 py-4">
                                <p id="phone_number_error" class="text-danger error-message mb-1" style="display: none"></p>
                                <table id="phone-number-summary" class="table table-bordered nowrap" style="font-size: 0.8rem; border-collapse: collapse; border-spacing: 0; width: 100%;">
                                    <thead>
                                        <tr>
                                            <th>Address Id.</th>
                                            <th>Address Data</th>
                                            <th>State Id.</th>
                                            <th>Address</th>
                                            <th>No. of User</th>
                                            <th>Package</th>
                                            <th>Phone Number</th>
                                            <th>Other Parameters</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be dynamically inserted here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="card mb-2">
                <a href="#quote-team-member-collapse" class="collapsed text-reset" data-bs-toggle="collapse">
                    <div class="py-3 px-4">

                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title rounded-circle bg-primary-subtle text-primary">
                                        04
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1 overflow-hidden">
                                <h5 class="font-size-16 mb-1">Personal In Charge</h5>
                                <small class="text-muted text-truncate"><i>Review and select staff name list to get email notifications (allow multiple selections)</i></small>
                            </div>
                            <div class="flex-shrink-0">
                                <i class="mdi mdi-chevron-up accor-down-icon font-size-24"></i>
                            </div>
                        </div>

                    </div>
                </a>

                <div id="quote-team-member-collapse" class="collapse show">
                    <div class="px-md-4 pb-3">
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-2 mb-md-3 row justify-content-md-start ps-md-4 px-4">    
                                    <label for="tad" class="col-md-3 col-form-label">List of Staff (TAD)</label>
                                    <div class="col-md-8">
                                        <select class="select2 form-control select2-multiple list_of_staff" name="tad[]" id="tad" multiple="multiple" data-placeholder="Search TM Staff..." style="width: 100%">
                                        @foreach (json_decode($order->project_team) as $staff)
                                            {{-- Add if role is TAD --}}
                                            @if($staff->role == 'TAD')
                                                    <option value="{{ $staff->name." (".$staff->email.")" }}" selected>{{ $staff->name }} ({{ $staff->email }})</option>
                                            @endif
                                        @endforeach
                                        </select>
                                        <small id="tad_error" class="text-danger error-message" style="display: none"></small>
                                    </div>
                            
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>{{-- end of custom-accordion --}}

        <div class="row my-4">
            <div class="col">
                <div class="text-end mt-2 mt-sm-0">
                    {{-- <btn class="btn btn-warning" id="btn-draft-order">Save as Draft</btn> --}}
                    <btn class="btn btn-primary" id="btn-submit-order">Submit</btn>
                    <btn class="btn btn-light" id="btn-back-order" onclick="window.history.back();">Back</btn>
                </div>
            </div> <!-- end col -->
        </div> <!-- end row-->



        {{-- Toast --}}
        <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
            <div id="success-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-success border-0">
                <div class="d-flex">
                <div class="toast-body" id="success-toast-message">
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
            </div>
        </div>
        <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
            <div id="failed-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-danger border-0">
                <div class="d-flex">
                <div class="toast-body" id="failed-toast-message">
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
            </div>
        </div>

    </div>

@endsection
@section('script')
    <script src="{{ URL::asset('/assets/libs/datatables/datatables.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/jszip/jszip.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/pdfmake/pdfmake.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/datatables.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/select2/select2.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/dropzone/dropzone.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/ecommerce-add-product.init.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/bootstrap-datepicker/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/datepicker/datepicker.min.js') }}"></script>    
    <script>
        $(document).ready(function() {

            // Enable CSRF Token for all ajax requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Auto-populate Order id in the page title
            var order = @json($order);
            $('.page-title').append(' [' + order.order_id + ']');

            // Change segment code option value based on selected segment name
            var lov = @json($lov['SEGMENTS']);
            $('#segment_group').change(function() {
                var selectedValue = $(this).val();
                // Generate new options based on the selected value
                var segmentCodes = lov.find(segment => segment.SEGMENT_GROUP === selectedValue)?.SEGMENT_CODE || [];
                var segmentCodeSelect = $('#segment_code');
                segmentCodeSelect.empty(); // Clear existing options
                segmentCodeSelect.append('<option value="">Select Segment Code...</option>'); // Add default option
                segmentCodes.forEach(function(code) {
                    segmentCodeSelect.append(`<option value="${code}">${code}</option>`);
                });
            });

            // Change selected segment code value based on segment group
            if (order.customer.customer_type == 'existing') {
                 var selectedValue = $('#segment_group').val();
                // Generate new options based on the selected value
                var segmentCodes = lov.find(segment => segment.SEGMENT_GROUP === selectedValue)?.SEGMENT_CODE || [];
                var segmentCodeSelect = $('#segment_code');
                segmentCodeSelect.empty(); // Clear existing options
                segmentCodeSelect.append('<option value="">Select Segment Code...</option>'); // Add default option
                segmentCodes.forEach(function(code) {
                    segmentCodeSelect.append(`<option value="${code}">${code}</option>`);
                });

                // Set the selected segment code
                var selectedSegmentCode = order.customer.segment_code;
                if (segmentCodes.includes(selectedSegmentCode)) {
                    segmentCodeSelect.val(selectedSegmentCode);
                }
            }

            const addressFields = ['unit_street', 'housing_area', 'city', 'postcode', 'state'];

            // console.log(order.address[0].state.name);

            // Check if the customer_type 'new' or 'existing'
            if (order.customer.customer_type == 'new') {
                // alert('Troubleshoot');
                // auto fill address id
                $('#address_id').val(order.address[0].id);
                // auto fill address fields
                addressFields.forEach(function (field) {
                    if (field == 'state') {
                        $(`#${field}`).val(order.address[0].state.name);
                    }
                    else {
                        $(`#${field}`).val(order.address[0][field]);
                    }

                });
            }
            else {
                // if (order.address.length > 1) {
                //     // If there are multiple addresses, set the address with is_primary = 1 as the billing address
                //     const primaryAddress = order.address.find(address => address.is_primary === 1);
                //     if (primaryAddress) {
                //         addressFields.forEach(function (field) {
                //             if (field == 'state') {
                //                 $(`#billing_${field}`).val(primaryAddress.state.name).prop('disabled', true);
                //             }
                //             else {
                //                 $(`#billing_${field}`).val(primaryAddress[field]).prop('disabled', true);
                //             }
                //         });
                //     }

                //     const secondaryAddress = order.address.find(address => address.is_primary === 0);
                //     if (secondaryAddress) {
                //         addressFields.forEach(function (field) {
                //             if (field == 'state') {
                //                 $(`#${field}`).val(secondaryAddress.state.name).prop('disabled', true);
                //             }
                //             else {
                //                 $(`#${field}`).val(secondaryAddress[field]).prop('disabled', true);
                //             }
                //         });
                //     }
                // }
                // else {
                //     // If there is only one address, set it as the billing address
                //     addressFields.forEach(function (field) {
                //         if (field == 'state') {
                //             $(`#billing_${field}`).val(order.address[0].state.name);
                //             $(`#${field}`).val(order.address[0].state.name).prop('disabled', true);
                //         }
                //         else {
                //             $(`#billing_${field}`).val(order.address[0][field]);
                //             $(`#${field}`).val(order.address[0][field]).prop('disabled', true);
                //         }
                //     });
                //     // auto-check billing address
                //     $('#checkBillingAddress').prop('checked', true);
                //     const isChecked = $('#checkBillingAddress').is(':checked');
                //     toggleBillingFields(isChecked, addressFields);
                // }
                // Find the primary address and fill in both general and billing address fields
                const primaryAddress = order.customer.addresses.find(address => address.is_primary === 1);
                if (primaryAddress) {
                    addressFields.forEach(function (field) {
                        if (field == 'state') {
                            $(`#billing_${field}`).val(primaryAddress.state.name).prop('disabled', true);
                            $(`#${field}`).val(primaryAddress.state.name).prop('disabled', true);
                        }
                        else {
                            $(`#billing_${field}`).val(primaryAddress[field]).prop('disabled', true);
                            $(`#${field}`).val(primaryAddress[field]).prop('disabled', true);
                        }
                    });
                    // auto-check billing address
                    $('#checkBillingAddress').prop('checked', true);
                    const isChecked = $('#checkBillingAddress').is(':checked');
                    toggleBillingFields(isChecked, addressFields);
                }
            }

            // Function to toggle billing fields
            $('#checkBillingAddress').on('change', function () {
                const isChecked = $(this).is(':checked');
                toggleBillingFields(isChecked, addressFields);

                if (isChecked) {
                    addressFields.forEach(function (field) {
                        const addressField = $(`#${field}`);
                        const billingField = $(`#billing_${field}`);
                        billingField.val(addressField.val()); // Copy address to billing address
                    });
                }
                else {
                    addressFields.forEach(function (field) {
                        $(`#billing_${field}`).val(''); // Clear billing address
                    });
                }
            });

            addressFields.forEach(function (field) {
                $(`#${field}`).on('input', function () {
                    if ($('#checkBillingAddress').is(':checked')) {
                        $(`#billing_${field}`).val($(this).val()); // Sync changes to billing address
                    }
                });
            });

            // Initialize the state of billing fields on page load
            // toggleBillingFields($('#checkBillingAddress').is(':checked'));

            // select postcode, reflect city and state
            $('#postcode').on('blur', function() {
                // Display loading icon
                $('#postcode-loading').show();
                var selectedValue = $(this).val();
                var route = "{{ route('postcode', ['postcode' => 'number']) }}";
                let modifiedRoute = route.replace("number", selectedValue);
                // Send AJAX request to fetch packages based on the selected provider
                $.ajax({
                    url: modifiedRoute,
                    method: 'GET',
                    success: function(data) {
                        // Handle the response data
                        // console.log(data);
                        // You can update the DOM with the fetched data here
                        $('#city').val(data.city);
                        $('#state').val(data.state.name);
                        // Hide loading icon
                        $('#postcode-loading').hide();
                    },
                    error: function(xhr, status, error) {
                        // Hide loading icon
                        $('#postcode-loading').hide();
                        console.error('Error fetching postcode:', error);
                    }
                });
            });

            // Auto populate city and state based on postcode
            $('#billing_postcode').on('blur', function() {
                // Display loading icon
                $('#billing-postcode-loading').show();
                var selectedValue = $(this).val();
                var route = "{{ route('postcode', ['postcode' => 'number']) }}";
                let modifiedRoute = route.replace("number", selectedValue);
                // Send AJAX request to fetch packages based on the selected provider
                $.ajax({
                    url: modifiedRoute,
                    method: 'GET',
                    success: function(data) {
                        // Handle the response data
                        // console.log(data);
                        // You can update the DOM with the fetched data here
                        $('#billing_city').val(data.city);
                        $('#billing_state').val(data.state.name);
                        // Hide loading icon
                        $('#billing-postcode-loading').hide();
                    },
                    error: function(xhr, status, error) {
                        // Hide loading icon
                        $('#billing-postcode-loading').hide();
                        console.error('Error fetching postcode:', error);
                    }
                });
            });

            // Auto populate city and state based on postcode
            $('#new_postcode').on('blur', function() {
                // Display loading icon
                $('#new-postcode-loading').show();
                var selectedValue = $(this).val();
                var route = "{{ route('postcode', ['postcode' => 'number']) }}";
                let modifiedRoute = route.replace("number", selectedValue);
                // Send AJAX request to fetch packages based on the selected provider
                $.ajax({
                    url: modifiedRoute,
                    method: 'GET',
                    success: function(data) {
                        // Handle the response data
                        // console.log(data);
                        // You can update the DOM with the fetched data here
                        $('#new_city').val(data.city);
                        $('#new_state').val(data.state.name);
                        $('#new_state_id').val(data.state.id);
                        // Hide loading icon
                        $('#new-postcode-loading').hide();
                    },
                    error: function(xhr, status, error) {
                        // Hide loading icon
                        $('#new-postcode-loading').hide();
                        console.error('Error fetching postcode:', error);
                    }
                });
            });

            // Search customer info by BRN
            $('#btn_search_customer').on('click', function(e) {
                e.preventDefault();
                var customerIdNum = $('#search_customer').val();
                
                // Hide any previous error messages
                $('#search_customer_error').hide();
                
                if (!customerIdNum) {
                    $('#search_customer_error').text('Please enter a customer ID/BRN to search').show();
                    return;
                }
                
                // For customer IDs with slashes, we need to handle them specially
                // We'll encode everything except slashes, which work correctly in the URL path
                var encodedCustomerIdNum = encodeURIComponent(customerIdNum).replace(/%2F/g, '/');
                var route = "{{ route('order.getCustomerInfoBrn', ['customerIdNum' => 'ID_PLACEHOLDER']) }}";
                let modifiedRoute = route.replace("ID_PLACEHOLDER", encodedCustomerIdNum);
                
                // Show loading indicator
                $('#btn_search_customer').text('Searching...').addClass('disabled');
                
                // Send AJAX request to fetch customer info
                $.ajax({
                    url: modifiedRoute,
                    method: 'GET',
                    success: function(response) {
                        // Handle the response data
                        console.log('Customer info response:', response);
                        
                        // Restore button
                        $('#btn_search_customer').text('Search').removeClass('disabled');
                        
                        // You can update the DOM with the fetched data here
                        if (response.success) {
                            // Handle successful response
                            console.log('Customer data retrieved successfully');
                            
                            var customerInfo = null;
                            
                            if (response.source=='NOVA') {
                                // Check if response.data.queryCustomerInfoNovaRes is an array or single object
                                if (Array.isArray(response.data.queryCustomerInfoNovaRes)) {
                                    // Take the first record from the array
                                    customerInfo = response.data.queryCustomerInfoNovaRes[0];
                                } else {
                                    // Single object
                                    customerInfo = response.data.queryCustomerInfoNovaRes;
                                }
                                
                                // Populate the form fields
                                $('#customer_name').val(customerInfo.customerName);
                                $('#brn').val(customerInfo.customerIdNumber);
                                
                                // Populate segment fields
                                $('#segment_group').val(customerInfo.segmentGroup);
                                
                                // For segment_code, we need to trigger the change event to populate the options first
                                $('#segment_group').trigger('change');
                                $('#segment_code').val(customerInfo.segmentCode);
                                
                                // For segment_sub_group, check if it's null and handle appropriately
                                if (customerInfo.segmentSubGroup !== null) {
                                    $('#segment_sub_group').val(customerInfo.segmentSubGroup);
                                } else {
                                    $('#segment_sub_group').val('');
                                }
                                
                                // Show success message
                                $('#search_customer_error').text('Successfully retrieved customer info from NOVA').removeClass('text-danger').addClass('text-success').show();
                            } else if (response.source=='ICP') {
                                // Check if response.data.queryCustomerInfoIcpRes is an array or single object
                                if (Array.isArray(response.data.queryCustomerInfoIcpRes)) {
                                    // Take the first record from the array
                                    customerInfo = response.data.queryCustomerInfoIcpRes[0];
                                } else {
                                    // Single object
                                    customerInfo = response.data.queryCustomerInfoIcpRes;
                                }
                                
                                // Populate the form fields
                                $('#customer_name').val(customerInfo.customerName);
                                $('#brn').val(customerInfo.customerIdNumber);
                                
                                // Populate segment fields
                                $('#segment_group').val(customerInfo.segmentGroup);
                                
                                // For segment_code, we need to trigger the change event to populate the options first
                                $('#segment_group').trigger('change');
                                $('#segment_code').val(customerInfo.segmentCode);
                                
                                // For segment_sub_group, check if it's null and handle appropriately
                                if (customerInfo.segmentSubGroup !== null) {
                                    $('#segment_sub_group').val(customerInfo.segmentSubGroup);
                                } else {
                                    $('#segment_sub_group').val('');
                                }
                                
                                // Show success message
                                $('#search_customer_error').text('Successfully retrieved customer info from ICP').removeClass('text-danger').addClass('text-success').show();
                            }
                        } else {
                            // Handle error response
                            $('#search_customer_error').text(response.message).removeClass('text-success').addClass('text-danger').show();
                            console.log('Error retrieving customer data:', response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        // Restore button
                        $('#btn_search_customer').text('Search').removeClass('disabled');
                        
                        // Show error message
                        var errorMessage = 'Error fetching customer info. Please try again.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        $('#search_customer_error').text(errorMessage).removeClass('text-success').addClass('text-danger').show();
                        
                        console.error('Error fetching customer info:', error);
                        console.log('Response:', xhr.responseText);
                    }
                });
            });

            // Tabulate the Packages & Users fields
            var quoteItems = @json($order->quote->quote_items);

            // Append all Packages & Users to a variable
            var packages_users = '';
            var packages_users_array = [];
            var packages = [];
            quoteItems.forEach(function (item) {
                // Check if quotable_type is 'App\Models\Package'
                if (item.quotable_type === 'App\\Models\\Package') {
                    packages_users += `${item.description} - ${item.quantity} users\n`;
                    // array of list of packages
                    packages = packages ? [...packages, item.description] : [item.description];
                    // array list of objects [package: 'package', users: 2] 
                    packages_users_array.push({
                        package: item.description,
                        users: item.quantity
                    });
                }
            })
            var users = quoteItems.reduce((total, item) => {
                if (item.quotable_type === 'App\\Models\\Package') {
                    return total + item.quantity;
                }
                return total;
            }, 0);

            // Append the Packages & Users to the textarea
            $('#packages_users').val(packages_users);

            // Initialize DataTable for phone number summary
            var table = $('#phone-number-summary').DataTable({
                paging: false,    // Disable paging
                ordering: false,  // Disable sorting
                searching: false,  // Disable searching
                info: false,      // Disable info
                scrollX: true,
                processing: true,
                columns: [
                    { data: "address_id", visible: false },  // Hide column
                    { data: "address_data", visible: false },  // Hide column
                    { data: "state_id", visible: false },  // Hide column
                    { data: "address", render: function(data, type, row) {
                        if (type === 'display') {
                            return data.replace(/, /g, '<br>');
                        }
                        return data;
                    } },
                    { data: "no_user" },
                    { data: "package" },
                    { data: "phone_number" },
                    { data: "other_parameters" },
                    { data: "action" },
                ],
                drawCallback:function(){
                    $('[data-bs-toggle="tooltip"]').tooltip(); // Reinitialize tooltips
                },      
            });

            // Add existing addresses to the table
            if (order.customer.customer_type == 'new'){
                var addresses = @json($order->address);
            }
            else {
                console.log('troubleshoot');
                var addresses = @json($order->address);
                // for existing customer, append the var addresses with customer's addresses
                var customer_addresses = @json($order->customer->addresses ? $order->customer->addresses : []);
                // combine the two arrays
                // Create a Map to track unique addresses
                const uniqueMap = new Map();
                // Combine and filter arrays to keep only unique addresses
                addresses = [...addresses, ...customer_addresses].filter(address => {
                    const key = `${address.unit_street}-${address.housing_area}`;
                    if (!uniqueMap.has(key)) {
                        uniqueMap.set(key, true);
                        return true;
                    }
                    return false;
                });
            }
            addresses.forEach(function (address) {
                table.row.add({
                    "address_id": address.id,
                    "address_data": {
                        "unit_street": address.unit_street,
                        "housing_area": address.housing_area,
                        "postcode": address.postcode,
                        "city": address.city,
                        "state": address.state.name
                    },
                    "state_id": address.state.id,
                    "address": `${address.unit_street}${address.housing_area ? ', ' + address.housing_area : ''}, ${address.postcode}, ${address.city}, ${address.state.name}`,
                    "no_user": '<div class="position-relative" style="width: 100px;"><input type="number" class="form-control no-spinner" name="no_user[]" value="" style="width: 100px;" inputmode="numeric" onkeydown="return event.keyCode !== 38 && event.keyCode !== 40;" onwheel="return false;" /><div class="no-user-loading position-absolute top-50 end-0 translate-middle-y me-3" style="display: none;"><div class="spinner-border spinner-border-sm text-secondary" role="status"><span class="visually-hidden">Loading...</span></div></div></div>',
                    "package": `<select class="form-select" name="package[]" style="width: 250px;">
                    ${packages.map(package => `<option value="${package}">${package}</option>`).join('')}</select>`,
                    "phone_number": '',
                    "other_parameters": '',
                    "action": "<a href='javascript:void(0);' name='btn-delete' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Remove Address' class='px-2 text-secondary'><i class='bx bx-trash font-size-20'></i></a> <a href='javascript:void(0);' name='btn-duplicate' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Duplicate Address' class='px-2 text-secondary'><i class='bx bx-copy font-size-20'></i></a>"
                });
            });
            table.draw();

            // Add new address to the table upon button btn-add-address click
            $("#btn-add-address").on("click", function(){
                // Validate the form
                var isValid = true;

                // Clear previous error messages
                $('.error-message').hide();

                // Validate each required field
                $('#new_unit_street, #new_postcode, #new_city, #new_state').each(function() {
                    var field = $(this);
                    var errorMessage = $('#' + field.attr('id') + '_error'); // Find corresponding error message element
                    if (field.val().trim() === '') {
                        isValid = false; // If field is empty, set isValid to false
                        errorMessage.text('This field is required.').show(); // Show the error message
                    }
                });

                if (isValid) {
                    table.row.add({
                        "address_id": '',
                        "address_data": {
                            "unit_street": $('#new_unit_street').val(),
                            "housing_area": $('#new_housing_area').val(),
                            "postcode": $('#new_postcode').val(),
                            "city": $('#new_city').val(),
                            "state": $('#new_state').val()
                        },
                        "state_id": $('#new_state_id').val(),
                        "address": `${$('#new_unit_street').val().replace(/,\s*$/, '')}${$('#new_housing_area').val() ? ', ' + $('#new_housing_area').val().replace(/,\s*$/, '') : ''}, ${$('#new_postcode').val()}, ${$('#new_city').val()}, ${$('#new_state').val()}`,
                        "no_user": '<div class="position-relative" style="width: 100px;"><input type="number" class="form-control no-spinner" name="no_user[]" value="" style="width: 100px;" /><div class="no-user-loading position-absolute top-50 end-0 translate-middle-y me-3" style="display: none;"><div class="spinner-border spinner-border-sm text-secondary" role="status"><span class="visually-hidden">Loading...</span></div></div></div>',
                        "package": `<select class="form-select" name="package[]" style="width: 250px;">${packages.map(package => `<option value="${package}">${package}</option>`).join('')}</select>`,
                        "phone_number": '',
                        "other_parameters": '',
                        "action": "<a href='javascript:void(0);' name='btn-delete' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Remove Address' class='px-2 text-secondary'><i class='bx bx-trash font-size-20'></i></a> <a href='javascript:void(0);' name='btn-duplicate' data-bs-toggle='tooltip' data-bs-placement='left' data-bs-title='Duplicate Address' class='px-2 text-secondary'><i class='bx bx-copy font-size-20'></i></a>"
                    });
                    table.draw();

                    // Re-validate duplicates after adding new row
                    setTimeout(function() {
                        var duplicates = findDuplicatePhoneNumbers(table);
                        highlightDuplicates(table, duplicates);
                    }, 100);

                    // Reset the form fields
                    $('#new_unit_street').val('');
                    $('#new_housing_area').val('');
                    $('#new_postcode').val('');
                    $('#new_city').val('');
                    $('#new_state').val('');
                    $('#new_state_id').val('');
                }
            });

            // Delete button functionality
            table.on('click', '[name="btn-delete"]', function() {
                var row = $(this).closest('tr'); // Find the row
                var rowIndex = table.row(row).index(); // Get the index of the row

                // Destroy tooltip for the delete button
                $(this).tooltip('dispose');

                // Remove the row and the subsequent 2 rows
                table.row(rowIndex).remove().draw();

                // Re-assign phone numbers
                assignPhoneNumber(table);

                // Re-validate duplicates after removing row
                setTimeout(function() {
                    var duplicates = findDuplicatePhoneNumbers(table);
                    highlightDuplicates(table, duplicates);
                }, 100);
            });

            // Duplicate button functionality
            table.on('click', '[name="btn-duplicate"]', function() {
                var row = $(this).closest('tr'); // Find the row
                var rowIndex = table.row(row).index(); // Get the index of the row

                // Destroy tooltip for the duplicate button
                // $(this).tooltip('dispose');

                // Clone the row and add it to the table
                var clonedRow = { ...table.row(row).data() }; // Create a deep copy of the row data
                clonedRow.address_id = clonedRow.address_id || ''; // Retain the original address_id or set to empty if undefined
                clonedRow.phone_number = ''; // Reset phone_number for the duplicated row
                table.row.add(clonedRow).draw();

                // Re-validate duplicates after duplicating row
                setTimeout(function() {
                    var duplicates = findDuplicatePhoneNumbers(table);
                    highlightDuplicates(table, duplicates);
                }, 100);

                // Re-assign phone numbers
                // assignPhoneNumber(table);
            });

            // Trigger no of user and package change event to generate phone number and FQDN
            // Use debounced version to prevent excessive calls
            const debouncedInputHandler = debounce(function(inputElement) {
                var row = $(inputElement).closest('tr');
                var table = $('#phone-number-summary').DataTable();
                
                // Show loading icon for this specific input
                $(inputElement).siblings('.no-user-loading').show();
                
                // Assign phone number only for the changed row
                assignPhoneNumberForRow(table, row);
                
                // Assign FQDN only for the changed row
                assignFqdnForRow(table, row);
            }, 500); // 500ms debounce delay

            table.on('input', 'input[name="no_user[]"]', function() {
                debouncedInputHandler(this);
            });

            table.on('change', 'select[name="package[]"]', function() {
                // Assign phone number on select change for package
                assignPhoneNumber(table);
                // Assign FQDN for supported packages
                assignFqdn(table);
            });

            // Initialize Select2 for Search
            $('#tad').select2({
                placeholder: "Search for a staff...",
                ajax: {
                    url: "{{ route('staff.search') }}",  // Your API route
                    dataType: 'json',
                    delay: 250,  // Delay for API calls to prevent too many requests
                    data: function(params) {
                        return {
                            info: params.term,  // The search term entered by the user
                        };
                    },
                    processResults: function(data) {
                        // Map the API response into the format that Select2 expects
                        return {
                            results: data.results.map(function(item) {
                                return {
                                    id: item.Name + ' (' + item.email + ')',  // Use Staff_No as the ID
                                    text: item.Name + ' (' + item.email + ')'    // Display "Staff_No - Name" as the text
                                };
                            })
                        };
                    },
                    cache: true
                },
                multiple: true,  // Allow multiple selections
                minimumInputLength: 2  // Start showing results after 2 characters
            });

            // Click submit button
            $('#btn-submit-order').on('click', function() {

                // Clear all error messages first
                $('.error-message').hide();
                $('[id$="_error"]').hide();

                // Validate no duplicate phone numbers before proceeding
                if (!validateNoDuplicates(table)) {
                    // Show error toast for duplicates
                    $('#failed-toast-message').text('Please resolve duplicate phone numbers before submitting.');
                    var failedToast = new bootstrap.Toast($('#failed-toast')[0], {
                        autohide: true,
                        delay: 5000
                    });
                    failedToast.show();
                    return false; // Prevent form submission
                }

                // temporary disable the button and change the text
                $(this).text('Submitting Order...').addClass('disabled');

                let tableData = [];
                table.rows().every(function() {
                    let row = $(this.node());
                    tableData.push({
                        address_id: this.data().address_id,
                        address_data: this.data().address_data,
                        state_id: this.data().state_id,
                        address: this.data().address,
                        no_user: row.find('input[name="no_user[]"]').val(),
                        package: row.find('select[name="package[]"]').val(),
                        fqdn: row.find('span[name="fqdn[]"]').length > 0 ? row.find('span[name="fqdn[]"]').text().replace('FQDN: ', '') : '',
                        service_ip: row.find('span[name="service_ip[]"]').length > 0 ? row.find('span[name="service_ip[]"]').text().replace('Service IP: ', '') : '',
                        public_ip: row.find('span[name="public_ip[]"]').length > 0 ? row.find('span[name="public_ip[]"]').text().replace('Public IP: ', '') : '',
                        phone_number: this.data().phone_number
                    });
                });

                console.log(tableData);

                // return false;

                const errors = {
                    emptyFields: 'Please fill in number of users and select packages',
                    userMismatch: 'Total number of users must be equal to the total number of users in Packages & Users field',
                    packageMismatch: 'Package and number of users must be the same as in Packages & Users field',
                    phoneNumberError: 'Please check availability of phone numbers in inventory',
                    ipAssignmentError: 'Zoom Package has failed IP assignments. Please resolve IP availability issues before proceeding.'
                };

                let total_user = 0;
                const order_package_user_array = [];

                // 1. Validate user numbers and packages
                for (const item of tableData) {
                    if (!item.no_user || !item.package) {
                        return showError(errors.emptyFields);
                    }

                    const usersCount = parseInt(item.no_user);
                    total_user += usersCount;

                    // Aggregate package users
                    const existingPackage = order_package_user_array.find(obj => obj.package === item.package);
                    if (existingPackage) {
                        existingPackage.users += usersCount;
                    } else {
                        order_package_user_array.push({ package: item.package, users: usersCount });
                    }
                }

                // 2. Validate total users against quote
                if (total_user !== users) {
                    return showError(errors.userMismatch);
                }

                // 3. Validate packages against quote
                // console.log(packages_users_array);
                if (JSON.stringify(order_package_user_array.sort((a, b) => a.package.localeCompare(b.package))) !== JSON.stringify(packages_users_array.sort((a, b) => a.package.localeCompare(b.package)))) {
                    return showError(errors.packageMismatch);
                }

                // 4. Validate phone numbers
                for (const item of tableData) {
                    if (item.phone_number.includes('*No available phone numbers') ||
                        item.phone_number.includes('*Not enough phone numbers available')) {
                        return showError(errors.phoneNumberError);
                    }
                }

                // 5. Validate IP assignments for Zoom packages
                for (const item of tableData) {
                    // Check if package is Zoom and has IP assignment errors
                    if (item.package && /zoom/i.test(item.package)) {
                        if (item.service_ip.includes('*No available Service IP') ||
                            item.public_ip.includes('*No available Public IP')) {
                            return showError(errors.ipAssignmentError);
                        }
                    }
                }

                // Send the data to the server using AJAX and formData
                console.log(tableData);
                var formData = new FormData();
                // formData.append('order_id', '{{ $order->id }}');
                formData.append('customer_type', '{{ $order->customer->customer_type }}');
                formData.append('customer_id', '{{ $order->customer->id ?? '' }}');

                // formData.append('quote_id', $('#quote_id').val());

                formData.append('customer_name', $('#customer_name').val());
                formData.append('sfdc_id', $('#sfdc_id').val());
                formData.append('brn', $('#brn').val());
                formData.append('account_number', $('#account_number').val());
                formData.append('segment_group', $('#segment_group').val());
                formData.append('segment_sub_group', $('#segment_sub_group').val());
                formData.append('segment_code', $('#segment_code').val());
                formData.append('person_in_charge', $('#person_in_charge').val());
                formData.append('position_title', $('#position_title').val());
                formData.append('department', $('#department').val());
                formData.append('contact_no', $('#contact_no').val());
                
                formData.append('address_id', $('#address_id').val());
                formData.append('unit_street', $('#unit_street').val());
                formData.append('housing_area', $('#housing_area').val());
                formData.append('postcode', $('#postcode').val());
                formData.append('city', $('#city').val());
                formData.append('state', $('#state').val());

                formData.append('billing_unit_street', $('#billing_unit_street').val());
                formData.append('billing_housing_area', $('#billing_housing_area').val());
                formData.append('billing_postcode', $('#billing_postcode').val());
                formData.append('billing_city', $('#billing_city').val());
                formData.append('billing_state', $('#billing_state').val());

                formData.append('order_type', $('#order_type').val());

                // Assign datatable to formData
                tableData.forEach((row, index) => {
                    formData.append(`assigned_detail[${index}][address_id]`, row.address_id);
                    formData.append(`assigned_detail[${index}][unit_street]`, row.address_data.unit_street);
                    formData.append(`assigned_detail[${index}][housing_area]`, row.address_data.housing_area);
                    formData.append(`assigned_detail[${index}][postcode]`, row.address_data.postcode);
                    formData.append(`assigned_detail[${index}][city]`, row.address_data.city);
                    formData.append(`assigned_detail[${index}][state]`, row.address_data.state);
                    formData.append(`assigned_detail[${index}][no_user]`, row.no_user);
                    formData.append(`assigned_detail[${index}][package]`, row.package);

                    // Add FQDN, service IP, and public IP if they exist
                    if (row.fqdn) {
                        formData.append(`assigned_detail[${index}][fqdn]`, row.fqdn);
                    }
                    if (row.service_ip) {
                        formData.append(`assigned_detail[${index}][service_ip]`, row.service_ip);
                    }
                    if (row.public_ip) {
                        formData.append(`assigned_detail[${index}][public_ip]`, row.public_ip);
                    }

                    // Convert phone_number string to array by splitting on '\n' and filter out empty values
                    const phoneNumbersArray = row.phone_number.split('\n').filter(number => number.trim() !== '');
                    phoneNumbersArray.forEach((phoneNumber, phoneIndex) => {
                        formData.append(`assigned_detail[${index}][phone_number][${phoneIndex}]`, phoneNumber);
                    });
                });

                // add select2 values to FormData
                const staffTypes = ['tad'];
                staffTypes.forEach(type => {
                    const selected = $(`#${type}`).val();
                    if (selected && selected.length > 0) {
                        selected.forEach(staffId => {
                            formData.append(`${type}[]`, staffId);
                        });
                    }
                });

                // Add method patch
                formData.append('_method', 'PATCH');

                $.ajax({
                    url: "{{ route('order.update', ['order' => $order->id]) }}",
                    method: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        // Handle the response from the server
                        console.log(response);
                        if (response.success) {
                            // Show the toast
                            $('#success-toast-message').text(response.message);
                            var toastElement = $('#success-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show();  // This show the toast
                            // Redirect to the quotation list page
                            setTimeout(function() {
                                window.location.href = "{{ route('order.index') }}";
                            }, 1500);
                        }
                    },
                    error: function(xhr, status, error) {
                        if (xhr.status === 422) {
                            console.log(xhr.responseJSON.errors);
                            var errors = xhr.responseJSON.errors;
                            var errorHtml = '<ul>';
                            $.each(errors, function(key, value) {
                                errorHtml += '<li>' + value[0] + '</li>';
                            });
                            errorHtml += '</ul>';
                            // create error message
                            $.each(errors, function(key, value) {
                                var errorElement = $('#' + key + '_error');
                                if (errorElement.length) {
                                    errorElement.text(value[0]).show();
                                    // Focus on the field with the error
                                    $('#' + key).focus();
                                } 
                                else {
                                    errorHtml += '<li>' + value[0] + '</li>';
                                }
                            });

                            // Show the toast
                            $('#failed-toast-message').html("Please fill in all required fields before submit the order.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        } else {
                            console.log(xhr.responseJSON);
                            // Show the toast
                            $('#failed-toast-message').html("Failed to submit quote. Please check and retry.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        }

                        // Enable the button and change value back to 'Update Quote'
                        $('#btn-submit-order').text('Submit').removeClass('disabled');

                    }
                });



            });

        });

        // Helper function to show error and toast
        function showError(message) {
            $('#phone_number_error').text(message).show();
            $('#failed-toast-message').html(message);
            const toast = new bootstrap.Toast($('#failed-toast')[0], {
                autohide: true,
                delay: 3000
            });
            toast.show();
            $('#btn-submit-order').text('Submit').removeClass('disabled');
            return false;
        }

        // Create a debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Store active AJAX requests to allow cancellation
        let activeRequests = {
            phoneNumber: null,
            fqdn: null
        };

        // Function to extract all assigned phone numbers from the table
        function getAllAssignedPhoneNumbers(table, excludeRowIndex = null) {
            var allPhoneNumbers = [];
            table.rows().every(function(index) {
                if (excludeRowIndex !== null && index === excludeRowIndex) {
                    return; // Skip the excluded row
                }

                var phoneNumberData = this.data().phone_number;
                if (phoneNumberData && typeof phoneNumberData === 'string') {
                    // Extract phone numbers from the stored data (could be HTML or plain text)
                    var phoneNumbers = phoneNumberData.replace(/<[^>]*>/g, '') // Remove HTML tags
                        .split(/[\n\r]/) // Split by newlines
                        .map(num => num.trim()) // Trim whitespace
                        .filter(num => num && !num.includes('*') && !num.includes('Not enough')); // Filter valid numbers

                    allPhoneNumbers = allPhoneNumbers.concat(phoneNumbers);
                }
            });
            return allPhoneNumbers;
        }

        // Function to find duplicate phone numbers in the table
        function findDuplicatePhoneNumbers(table) {
            var phoneNumberCounts = {};
            var duplicates = [];

            table.rows().every(function(index) {
                var phoneNumberData = this.data().phone_number;
                if (phoneNumberData && typeof phoneNumberData === 'string') {
                    var phoneNumbers = phoneNumberData.replace(/<[^>]*>/g, '')
                        .split(/[\n\r]/)
                        .map(num => num.trim())
                        .filter(num => num && !num.includes('*') && !num.includes('Not enough'));

                    phoneNumbers.forEach(function(phoneNumber) {
                        if (phoneNumberCounts[phoneNumber]) {
                            phoneNumberCounts[phoneNumber].push(index);
                        } else {
                            phoneNumberCounts[phoneNumber] = [index];
                        }
                    });
                }
            });

            // Find phone numbers that appear in multiple rows
            Object.keys(phoneNumberCounts).forEach(function(phoneNumber) {
                if (phoneNumberCounts[phoneNumber].length > 1) {
                    duplicates.push({
                        phoneNumber: phoneNumber,
                        rowIndexes: phoneNumberCounts[phoneNumber]
                    });
                }
            });

            return duplicates;
        }

        // Function to highlight duplicate phone numbers
        function highlightDuplicates(table, duplicates) {
            // Clear previous highlights
            clearDuplicateHighlights(table);

            if (duplicates.length > 0) {
                duplicates.forEach(function(duplicate) {
                    duplicate.rowIndexes.forEach(function(rowIndex) {
                        var row = table.row(rowIndex).node();
                        $(row).find('td:eq(3)').addClass('duplicate-phone-number'); // Phone Number column (visible index 3)
                    });
                });

                // Show error message
                var errorMessage = 'Duplicate phone numbers detected: ' +
                    duplicates.map(d => d.phoneNumber).join(', ');
                $('#phone_number_error').text(errorMessage).show();

                // Disable submit button
                $('#btn-submit-order').addClass('submit-disabled');

                return false; // Validation failed
            } else {
                // Hide error message and enable submit button
                $('#phone_number_error').hide();
                $('#btn-submit-order').removeClass('submit-disabled');
                return true; // Validation passed
            }
        }

        // Function to clear duplicate highlights
        function clearDuplicateHighlights(table) {
            table.rows().every(function() {
                var row = this.node();
                $(row).find('td').removeClass('duplicate-phone-number');
            });
        }

        // Function to validate no duplicates exist (for form submission)
        function validateNoDuplicates(table) {
            var duplicates = findDuplicatePhoneNumbers(table);
            return highlightDuplicates(table, duplicates);
        }

        // Function to assign phone number
        function assignPhoneNumber(table) {
            // Get the value of the no of user and package for all row available
            // sample data = [{'row': 0, 'postcode': '40160', 'no_user': 1, 'package': 'Package A'}, {'row': 1, 'no_user': 2, 'package': 'Package B'}]
            var table_details = [];
            table.rows().every(function(index, element) {
                var rowNode = $(table.row(this).node()); // Access the DOM node of the row
                var noUser = rowNode.find('input[name="no_user[]"]').val();
                var package = rowNode.find('select[name="package[]"]').val();
                var stateId = table.row(this).data().state_id; // Get state_id from the DataTable row data
                table_details.push({'row': index, 'no_user': noUser, 'package': package, 'state_id': stateId});
            });

            // console.log(table_details);

            // alert(JSON.stringify(info));
            // Send the data to the server using AJAX for phone number assignment
            $.ajax({
                url: "{{ route('phone-number-inventory.assign') }}",
                method: 'POST',
                data: {
                    info: table_details,
                },
                success: function(response) {
                    // console.log(response);
                    // Handle the response from the server
                    if (Array.isArray(response.data)) {
                        response.data.forEach(function(item) {
                            var row = table.row(item.row).node();
                            // Check item.phone_numbers is an array and has values
                            if (Array.isArray(item.phone_numbers)) {
                                if (item.phone_numbers && item.phone_numbers.length > 0) {
                                    if (item.no_user == item.phone_numbers.length) {
                                        // Successfully assigned phone numbers
                                        $(row).find('td:eq(3)').html(item.phone_numbers.join('<br>'));
                                        table.row(row).data().phone_number = item.phone_numbers.join('\n'); // Update DataTable's internal data
                                    }
                                    else {
                                        // Not enough phone numbers available
                                        $(row).find('td:eq(3)').html(item.phone_numbers.join('<br>') + '<br><span class="text-danger">*Not enough phone numbers available</span>');
                                        table.row(row).data().phone_number = item.phone_numbers.join('<br>') + '<br><span class="text-danger">*Not enough phone numbers available</span>';
                                    }
                                }
                                else {
                                    // User = 0
                                    $(row).find('td:eq(3)').html('');
                                     table.row(row).data().phone_number = '';
                                }
                            }
                            else {
                                // No available phone numbers
                                $(row).find('td:eq(3)').html('<span class="text-danger">*' + item.phone_numbers + '</span>');
                                table.row(row).data().phone_number = '<span class="text-danger">*' + item.phone_numbers + '</span>';
                            }
                        });
                    } else {
                        console.error('Response is not an array:', response.data);
                    }
                    
                    // Hide all loading icons after processing
                    $('#phone-number-summary .no-user-loading').hide();

                    // After processing all phone number assignments, check for duplicates
                    setTimeout(function() {
                        var duplicates = findDuplicatePhoneNumbers(table);
                        highlightDuplicates(table, duplicates);
                    }, 100);

                    // console.log(response);
                    // Update the phone number field in the table
                    // $('#phone-number-summary tbody tr').each(function(index) {
                    //     $(this).find('td:eq(3)').text(response[index].phone_number);
                    // });
                },
                error: function(xhr, status, error) {
                    // Hide all loading icons on error as well
                    $('#phone-number-summary .no-user-loading').hide();
                    console.error('Error assigning phone number:', error);
                }
            });
        }

        // Function to assign FQDN, Service IP, and Public IP for supported packages
        function assignFqdn(table) {
            var order = @json($order);
            var customerType = order.customer.customer_type;
            var orderId = order.id;
            var customerId = order.customer.id ?? null;

            // Get package and no_user information for all rows (similar to assignPhoneNumber approach)
            var table_details = [];
            table.rows().every(function(index, element) {
                var rowNode = $(table.row(this).node());
                var packageValue = rowNode.find('select[name="package[]"]').val();
                var noUserValue = rowNode.find('input[name="no_user[]"]').val();
                table_details.push({'row': index, 'package': packageValue, 'no_user': noUserValue});
            });

            // Process each row for FQDN assignment
            table_details.forEach(function(item) {
                var row = table.row(item.row).node();
                var packageValue = item.package;
                var noUserValue = item.no_user;

                // Check if assignments already exist to avoid multiple AJAX calls
                var currentContent = $(row).find('td:eq(4)').html(); // Other Parameters is at visible index 4
                var hasAssignments = currentContent && (currentContent.includes('FQDN:') || currentContent.includes('Service IP:') || currentContent.includes('Public IP:'));

                // Only assign for supported packages and when no_user > 0
                if (packageValue && /teams|zoom|webex/i.test(packageValue) && noUserValue && parseInt(noUserValue) > 0) {

                    // Determine current package type
                    var isZoom = /zoom/i.test(packageValue);
                    var isTeams = /teams/i.test(packageValue);
                    var isWebex = /webex/i.test(packageValue);

                    // Check if existing assignments match the current package type
                    var assignmentsMatchPackage = false;
                    if (hasAssignments) {
                        if (isZoom) {
                            // Zoom should have FQDN + Service IP + Public IP
                            assignmentsMatchPackage = currentContent.includes('FQDN:') &&
                                                    currentContent.includes('Service IP:') &&
                                                    currentContent.includes('Public IP:');
                        } else if (isTeams || isWebex) {
                            // Teams/Webex should have only FQDN (no Service IP or Public IP)
                            var hasCorrectPattern = currentContent.includes('FQDN:') &&
                                                  !currentContent.includes('Service IP:') &&
                                                  !currentContent.includes('Public IP:');

                            if (hasCorrectPattern) {
                                // Extract FQDN value from current content to check package type match
                                var fqdnMatch = currentContent.match(/FQDN:\s*([^\s<]+)/);
                                if (fqdnMatch && fqdnMatch[1]) {
                                    var currentFqdn = fqdnMatch[1];

                                    // Check if FQDN matches the selected package type
                                    if (isTeams) {
                                        // Teams FQDN should contain 'teams' subdomain
                                        assignmentsMatchPackage = currentFqdn.includes('.teams.');
                                    } else if (isWebex) {
                                        // Webex FQDN should contain 'webex' subdomain
                                        assignmentsMatchPackage = currentFqdn.includes('.webex.');
                                    }

                                    console.log('FQDN package type check:', {
                                        currentFqdn: currentFqdn,
                                        isTeams: isTeams,
                                        isWebex: isWebex,
                                        assignmentsMatchPackage: assignmentsMatchPackage
                                    });
                                }
                            }
                        }
                    }

                    // Only retain assignments if they match current package type AND no_user > 1 (multi-digit retention)
                    if (hasAssignments && assignmentsMatchPackage && parseInt(noUserValue) > 1) {
                        console.log('Assignments match package type and no_user > 1, retaining:', currentContent);
                        return; // Skip AJAX calls, keep existing assignments
                    }

                    // If we reach here, either:
                    // 1. No existing assignments
                    // 2. Existing assignments don't match current package type (package changed)
                    // 3. no_user = 1 (first assignment)
                    // In all cases, proceed with new assignment

                    // Show loading indicator in Other Parameters column (visible index 4)
                    $(row).find('td:eq(4)').html('<div class="spinner-border spinner-border-sm text-secondary" role="status"><span class="visually-hidden">Loading assignments...</span></div>');

                    // Track assignment results
                    var assignmentResults = {
                        fqdn: null,
                        serviceIp: null,
                        publicIp: null,
                        completed: 0,
                        total: isZoom ? 3 : 1 // Zoom gets all 3, others get only FQDN
                    };

                    // Function to update display when all assignments are complete
                    function updateAssignmentDisplay() {
                        if (assignmentResults.completed >= assignmentResults.total) {
                            var displayHtml = '';

                            if (assignmentResults.fqdn) {
                                displayHtml += '<span name="fqdn[]" style="display: inline-block; width: 250px; word-wrap: break-word; white-space: normal;">FQDN: ' + assignmentResults.fqdn + '</span>';
                            }

                            if (assignmentResults.serviceIp) {
                                if (displayHtml) displayHtml += '<br>';
                                // Check if it's an error message and apply appropriate styling
                                if (assignmentResults.serviceIp.startsWith('*No available')) {
                                    displayHtml += '<span name="service_ip[]" style="display: inline-block; width: 200px; word-wrap: break-word; white-space: normal; color: red;">Service IP: ' + assignmentResults.serviceIp + '</span>';
                                } else {
                                    displayHtml += '<span name="service_ip[]" style="display: inline-block; width: 200px; word-wrap: break-word; white-space: normal;">Service IP: ' + assignmentResults.serviceIp + '</span>';
                                }
                            }

                            if (assignmentResults.publicIp) {
                                if (displayHtml) displayHtml += '<br>';
                                // Check if it's an error message and apply appropriate styling
                                if (assignmentResults.publicIp.startsWith('*No available')) {
                                    displayHtml += '<span name="public_ip[]" style="display: inline-block; width: 200px; word-wrap: break-word; white-space: normal; color: red;">Public IP: ' + assignmentResults.publicIp + '</span>';
                                } else {
                                    displayHtml += '<span name="public_ip[]" style="display: inline-block; width: 200px; word-wrap: break-word; white-space: normal;">Public IP: ' + assignmentResults.publicIp + '</span>';
                                }
                            }

                            // Update the display
                            $(row).find('td:eq(4)').html(displayHtml);
                            table.row(row).data().other_parameters = displayHtml;
                        }
                    }

                    // Function to handle assignment errors
                    function handleAssignmentError(type, error) {
                        console.error(type + ' assignment error:', error);
                        assignmentResults.completed++;
                        updateAssignmentDisplay();
                    }

                    // 1. FQDN Assignment (for all supported packages)
                    $.ajax({
                        url: "{{ route('fqdn.check-or-create') }}",
                        method: 'POST',
                        data: {
                            package: packageValue,
                            order_id: orderId,
                            customer_id: customerId,
                            customer_type: customerType,
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            console.log('FQDN assignment response:', response);
                            if (response.fqdn) {
                                assignmentResults.fqdn = response.fqdn;
                            }
                            assignmentResults.completed++;
                            updateAssignmentDisplay();
                        },
                        error: function(xhr, status, error) {
                            handleAssignmentError('FQDN', error);
                        }
                    });

                    // 2. Service IP Assignment (only for Zoom)
                    if (isZoom) {
                        $.ajax({
                            url: "{{ route('service-ip.check-or-create') }}",
                            method: 'POST',
                            data: {
                                package: packageValue,
                                order_id: orderId,
                                customer_id: customerId,
                                customer_type: customerType,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                console.log('Service IP assignment response:', response);
                                if (response.service_ip) {
                                    assignmentResults.serviceIp = response.service_ip;
                                }
                                assignmentResults.completed++;
                                updateAssignmentDisplay();
                            },
                            error: function(xhr, status, error) {
                                handleAssignmentError('Service IP', error);
                            }
                        });

                        // 3. Public IP Assignment (only for Zoom)
                        $.ajax({
                            url: "{{ route('public-ip.check-or-create') }}",
                            method: 'POST',
                            data: {
                                package: packageValue,
                                order_id: orderId,
                                customer_id: customerId,
                                customer_type: customerType,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                console.log('Public IP assignment response:', response);
                                if (response.public_ip) {
                                    assignmentResults.publicIp = response.public_ip;
                                }
                                assignmentResults.completed++;
                                updateAssignmentDisplay();
                            },
                            error: function(xhr, status, error) {
                                handleAssignmentError('Public IP', error);
                            }
                        });
                    }

                } else {
                    // Clear assignments if package is not supported or no_user is 0
                    $(row).find('td:eq(4)').html('');
                    // Update DataTable's internal data without calling .data() to avoid input field reset
                    table.row(row).data().other_parameters = '';
                }
            });
        }

        // Modified assignPhoneNumber function to process only specific row
        function assignPhoneNumberForRow(table, row) {
            // Cancel previous request if still active
            if (activeRequests.phoneNumber) {
                activeRequests.phoneNumber.abort();
            }
            
            // Get data only for the specific row
            var rowData = table.row(row).data();
            var rowIndex = table.row(row).index();
            var rowNode = $(row);
            
            var noUserValue = rowNode.find('input[name="no_user[]"]').val();
            var packageValue = rowNode.find('select[name="package[]"]').val();
            var stateId = rowData.state_id;
            
            // Only proceed if both no_user and package have values
            if (noUserValue > 0 && packageValue !== '') {
                var table_details = [{
                    'row': rowIndex,
                    'postcode': rowData.postcode,
                    'no_user': noUserValue,
                    'package': packageValue,
                    'state_id': stateId
                }];
                
                // Send AJAX request with only this row's data
                activeRequests.phoneNumber = $.ajax({
                    url: "{{ route('phone-number-inventory.assign') }}",
                    method: 'POST',
                    data: {
                        info: table_details,
                    },
                    success: function(response) {
                        // Handle the response for this specific row
                        if (Array.isArray(response.data) && response.data.length > 0) {
                            var item = response.data[0];
                            var rowNode = table.row(item.row).node();
                            
                            if (Array.isArray(item.phone_numbers)) {
                                if (item.phone_numbers && item.phone_numbers.length > 0) {
                                    if (item.no_user == item.phone_numbers.length) {
                                        // Successfully assigned phone numbers
                                        $(rowNode).find('td:eq(3)').html(item.phone_numbers.join('<br>'));
                                        table.row(rowNode).data().phone_number = item.phone_numbers.join('\n');
                                    }
                                }
                            } else {
                                // No available phone numbers
                                $(rowNode).find('td:eq(3)').html('<span class="text-danger">*' + item.phone_numbers + '</span>');
                                table.row(rowNode).data().phone_number = '<span class="text-danger">*' + item.phone_numbers + '</span>';
                            }
                        }

                        // Hide loading icon
                        row.find('.no-user-loading').hide();

                        // After updating this row, check for duplicates across all rows
                        setTimeout(function() {
                            var duplicates = findDuplicatePhoneNumbers(table);
                            highlightDuplicates(table, duplicates);
                        }, 100);
                    },
                    error: function(xhr, status, error) {
                        // Hide loading icon on error
                        row.find('.no-user-loading').hide();
                        
                        // Only show error if not aborted
                        if (status !== 'abort') {
                            console.error('Error assigning phone number:', error);
                        }
                    }
                });
            } else {
                // Clear phone numbers if no_user is 0 or package is empty
                row.find('td:eq(3)').html('');
                table.row(row).data().phone_number = '';
                row.find('.no-user-loading').hide();
            }
        }

        // Modified assignFqdn function to process only specific row
        function assignFqdnForRow(table, row) {
            // Cancel previous request if still active
            if (activeRequests.fqdn) {
                activeRequests.fqdn.abort();
            }
            
            var order = @json($order);
            var customerType = order.customer.customer_type;
            var orderId = order.id;
            var customerId = order.customer.id ?? null;
            
            // Get data only for the specific row
            var rowNode = $(row);
            var packageValue = rowNode.find('select[name="package[]"]').val();
            var noUserValue = rowNode.find('input[name="no_user[]"]').val();
            
            // Only assign for supported packages and when no_user > 0
            if (packageValue && /teams|zoom|webex/i.test(packageValue) && noUserValue && parseInt(noUserValue) > 0) {
                // Check if assignments already exist to avoid multiple AJAX calls
                var currentContent = row.find('td:eq(4)').html();
                var hasAssignments = currentContent && (currentContent.includes('FQDN:') || currentContent.includes('Service IP:') || currentContent.includes('Public IP:'));
                
                // Determine current package type
                var isZoom = /zoom/i.test(packageValue);
                var isTeams = /teams/i.test(packageValue);
                var isWebex = /webex/i.test(packageValue);
                
                // Check if existing assignments match the current package type
                var assignmentsMatchPackage = false;
                if (hasAssignments) {
                    if (isZoom) {
                        // Zoom should have FQDN + Service IP + Public IP
                        assignmentsMatchPackage = currentContent.includes('FQDN:') &&
                                        currentContent.includes('Service IP:') &&
                                        currentContent.includes('Public IP:');
                    } else if (isTeams || isWebex) {
                        // Teams/Webex should have only FQDN
                        var hasCorrectPattern = currentContent.includes('FQDN:') &&
                                      !currentContent.includes('Service IP:') &&
                                      !currentContent.includes('Public IP:');
                        assignmentsMatchPackage = hasCorrectPattern;
                    }
                }
                
                // Only proceed if assignments don't match or don't exist
                if (!hasAssignments || !assignmentsMatchPackage) {
                    // Initialize assignment results for this row
                    var assignmentResults = {
                        total: isZoom ? 3 : 1, // Zoom needs FQDN + Service IP + Public IP
                        completed: 0,
                        fqdn: null,
                        serviceIp: null,
                        publicIp: null
                    };
                    
                    // Function to update display when all assignments are complete
                    function updateAssignmentDisplay() {
                        if (assignmentResults.completed >= assignmentResults.total) {
                            var displayHtml = '';
                            
                            if (assignmentResults.fqdn) {
                                displayHtml += '<span name="fqdn[]" style="display: inline-block; width: 250px; word-wrap: break-word; white-space: normal;">FQDN: ' + assignmentResults.fqdn + '</span>';
                            }
                            
                            if (assignmentResults.serviceIp) {
                                if (displayHtml) displayHtml += '<br>';
                                // Check if it's an error message and apply appropriate styling
                                if (assignmentResults.serviceIp.startsWith('*No available')) {
                                    displayHtml += '<span name="service_ip[]" style="display: inline-block; width: 200px; word-wrap: break-word; white-space: normal; color: red;">Service IP: ' + assignmentResults.serviceIp + '</span>';
                                } else {
                                    displayHtml += '<span name="service_ip[]" style="display: inline-block; width: 200px; word-wrap: break-word; white-space: normal;">Service IP: ' + assignmentResults.serviceIp + '</span>';
                                }
                            }
                            
                            if (assignmentResults.publicIp) {
                                if (displayHtml) displayHtml += '<br>';
                                // Check if it's an error message and apply appropriate styling
                                if (assignmentResults.publicIp.startsWith('*No available')) {
                                    displayHtml += '<span name="public_ip[]" style="display: inline-block; width: 200px; word-wrap: break-word; white-space: normal; color: red;">Public IP: ' + assignmentResults.publicIp + '</span>';
                                } else {
                                    displayHtml += '<span name="public_ip[]" style="display: inline-block; width: 200px; word-wrap: break-word; white-space: normal;">Public IP: ' + assignmentResults.publicIp + '</span>';
                                }
                            }
                            
                            row.find('td:eq(4)').html(displayHtml);
                        }
                    }
                    
                    // Function to handle assignment errors
                    function handleAssignmentError(type, error) {
                        assignmentResults.completed++;
                        console.error(type + ' assignment error:', error);
                        updateAssignmentDisplay();
                    }
                    
                    // 1. FQDN Assignment (for all supported packages)
                    activeRequests.fqdn = $.ajax({
                        url: "{{ route('fqdn.check-or-create') }}",
                        method: 'POST',
                        data: {
                            package: packageValue,
                            order_id: orderId,
                            customer_id: customerId,
                            customer_type: customerType,
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response.fqdn) {
                                assignmentResults.fqdn = response.fqdn;
                            }
                            assignmentResults.completed++;
                            updateAssignmentDisplay();
                        },
                        error: function(xhr, status, error) {
                            // Only show error if not aborted
                            if (status !== 'abort') {
                                handleAssignmentError('FQDN', error);
                            }
                        }
                    });
                    
                    // 2. Service IP and Public IP Assignment (for Zoom packages only)
                    if (isZoom) {
                        // Service IP Assignment
                        $.ajax({
                            url: "{{ route('service-ip.check-or-create') }}",
                            method: 'POST',
                            data: {
                                package: packageValue,
                                order_id: orderId,
                                customer_id: customerId,
                                customer_type: customerType,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.service_ip) {
                                    assignmentResults.serviceIp = response.service_ip;
                                }
                                assignmentResults.completed++;
                                updateAssignmentDisplay();
                            },
                            error: function(xhr, status, error) {
                                handleAssignmentError('Service IP', error);
                            }
                        });
                        
                        // Public IP Assignment
                        $.ajax({
                            url: "{{ route('public-ip.check-or-create') }}",
                            method: 'POST',
                            data: {
                                package: packageValue,
                                order_id: orderId,
                                customer_id: customerId,
                                customer_type: customerType,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.public_ip) {
                                    assignmentResults.publicIp = response.public_ip;
                                }
                                assignmentResults.completed++;
                                updateAssignmentDisplay();
                            },
                            error: function(xhr, status, error) {
                                handleAssignmentError('Public IP', error);
                            }
                        });
                    }
                }
            } else {
                // Clear assignments if package is not supported or no_user is 0
                row.find('td:eq(4)').html('');
            }
        }

        // Toggle the billing address fields
        function toggleBillingFields(disable, addressFields) {
            if (disable) {
                addressFields.forEach(function (field) {
                    $(`#billing_${field}`).prop('disabled', true);
                });
            }
            else {
                addressFields.forEach(function (field) {
                    $(`#billing_${field}`).prop('disabled', false);
                });
            }
        }
    </script>
@endsection
